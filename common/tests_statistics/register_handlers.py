import logging
from aiogram import F
from aiogram.types import CallbackQuery
from aiogram.fsm.context import FSMContext

# Настраиваем логгер
logger = logging.getLogger(__name__)
from common.tests_statistics.menu import show_tests_statistics_menu
from common.tests_statistics.handlers import (
    show_course_entry_groups, show_month_entry_groups, show_month_entry_months,
    show_course_entry_statistics, show_month_entry_statistics,
    show_month_control_groups, show_month_control_months, show_month_control_statistics,
    show_ent_groups, show_ent_students, show_ent_statistics,
    show_tests_comparison,
    show_course_entry_detailed_microtopics, show_course_entry_summary_microtopics,
    show_month_entry_detailed_microtopics, show_month_entry_summary_microtopics,
    show_month_control_detailed_microtopics, show_month_control_summary_microtopics,
    handle_course_entry_back_to_users, handle_course_entry_back_to_result,
    handle_month_entry_back_to_students, handle_month_entry_back_to_result,
    handle_month_control_back_to_students, handle_month_control_back_to_result
)

def get_transitions_handlers(states_group, role):
    """
    Создает словари переходов и обработчиков для статистики тестов

    Args:
        states_group: Группа состояний (CuratorTestsStatisticsStates или TeacherTestsStatisticsStates)
        role: Роль пользователя ('curator' или 'teacher')

    Returns:
        tuple: (STATE_TRANSITIONS, STATE_HANDLERS) - словари переходов и обработчиков
    """
    # Создаем словарь переходов между состояниями (аналогично аналитике)
    STATE_TRANSITIONS = {
        states_group.main: None,  # None означает возврат в главное меню

        # Переходы для входного теста курса (работа с незарегистрированными пользователями)
        states_group.course_entry_select_subject: states_group.main,
        states_group.course_entry_select_user: states_group.course_entry_select_subject,
        states_group.course_entry_result: states_group.course_entry_select_user,
        states_group.course_entry_result_display: states_group.course_entry_result,  # Возврат к результату

        # Переходы для входного теста месяца (работа со студентами)
        states_group.month_entry_select_group: states_group.main,
        states_group.month_entry_select_month: states_group.month_entry_select_group,
        states_group.month_entry_select_student: states_group.month_entry_select_month,
        states_group.month_entry_result: states_group.month_entry_select_student,
        states_group.month_entry_result_display: states_group.month_entry_result,  # Возврат к результату

        # Переходы для контрольного теста месяца (работа со студентами)
        states_group.month_control_select_group: states_group.main,
        states_group.month_control_select_month: states_group.month_control_select_group,
        states_group.month_control_select_student: states_group.month_control_select_month,
        states_group.month_control_result: states_group.month_control_select_student,
        states_group.month_control_result_display: states_group.month_control_result,  # Возврат к результату

        # Переходы для пробного ЕНТ (работа со студентами)
        states_group.ent_select_group: states_group.main,
        states_group.ent_select_student: states_group.ent_select_group,
        states_group.ent_result: states_group.ent_select_student,
        states_group.ent_result_display: states_group.ent_result  # Возврат к результату (будет переопределено для истории)
    }

    # Добавляем переходы для истории пробного ЕНТ, если состояния существуют
    if hasattr(states_group, 'ent_history'):
        STATE_TRANSITIONS.update({
            # Переходы для истории пробного ЕНТ
            states_group.ent_history: states_group.ent_select_student,  # Из списка истории назад к выбору студента
            states_group.ent_history_detail: states_group.ent_history,  # Из детального просмотра к списку тестов
            states_group.ent_history_analytics_subjects: states_group.ent_history_detail,  # Из выбора предметов к детальному просмотру
            states_group.ent_history_subject_analytics_menu: states_group.ent_history_analytics_subjects,  # Из меню аналитики к выбору предметов
            states_group.ent_history_subject_analytics_detailed: states_group.ent_history_subject_analytics_menu,  # Из детальной аналитики к меню
            states_group.ent_history_subject_analytics_summary: states_group.ent_history_subject_analytics_menu,  # Из сводки к меню
        })

        # Переопределяем переход для ent_result_display в контексте истории
        # Из детального просмотра результата должны возвращаться к истории, а не к результату
        STATE_TRANSITIONS[states_group.ent_result_display] = states_group.ent_history

        # Переопределяем переход для ent_result когда мы в контексте истории
        # Это нужно сделать после основных переходов, чтобы переопределить базовый переход
        STATE_TRANSITIONS[states_group.ent_result] = states_group.ent_history

    # Добавляем переходы для студенческих состояний, которые могут использоваться в контексте истории ЕНТ
    from student.handlers.trial_ent import TrialEntStates
    STATE_TRANSITIONS.update({
        TrialEntStates.subject_analytics_menu: states_group.ent_history_analytics_subjects if hasattr(states_group, 'ent_history_analytics_subjects') else states_group.main,
        TrialEntStates.subject_analytics_detailed: TrialEntStates.subject_analytics_menu,
        TrialEntStates.subject_analytics_summary: TrialEntStates.subject_analytics_menu,
    })

    # Добавляем переходы для состояний микротем, если они есть
    if hasattr(states_group, 'month_entry_detailed_microtopics'):
        STATE_TRANSITIONS[states_group.month_entry_detailed_microtopics] = states_group.month_entry_result
    if hasattr(states_group, 'month_entry_summary_microtopics'):
        STATE_TRANSITIONS[states_group.month_entry_summary_microtopics] = states_group.month_entry_result

    # Создаем словарь обработчиков для каждого состояния (аналогично аналитике)
    STATE_HANDLERS = {
        states_group.main: lambda callback, state, user_role=None: show_tests_statistics_menu(callback, state, user_role or role),

        # Обработчики для входного теста курса (незарегистрированные пользователи)
        states_group.course_entry_select_subject: lambda callback, state, user_role=None: show_course_entry_groups(callback, state),
        states_group.course_entry_select_user: lambda callback, state, user_role=None: handle_course_entry_back_to_users(callback, state),
        states_group.course_entry_result: lambda callback, state, user_role=None: handle_course_entry_back_to_result(callback, state),
        states_group.course_entry_result_display: lambda callback, state, user_role=None: handle_course_entry_back_to_result(callback, state),

        # Обработчики для входного теста месяца (студенты)
        states_group.month_entry_select_group: lambda callback, state, user_role=None: show_month_entry_groups(callback, state),
        states_group.month_entry_select_month: lambda callback, state, user_role=None: show_month_entry_months(callback, state),
        states_group.month_entry_select_student: lambda callback, state, user_role=None: handle_month_entry_back_to_students(callback, state),
        states_group.month_entry_result: lambda callback, state, user_role=None: handle_month_entry_back_to_result(callback, state),
        states_group.month_entry_result_display: lambda callback, state, user_role=None: handle_month_entry_back_to_result(callback, state),

        # Обработчики для контрольного теста месяца (студенты)
        states_group.month_control_select_group: lambda callback, state, user_role=None: show_month_control_groups(callback, state),
        states_group.month_control_select_month: lambda callback, state, user_role=None: show_month_control_months(callback, state),
        states_group.month_control_select_student: lambda callback, state, user_role=None: handle_month_control_back_to_students(callback, state),
        states_group.month_control_result: lambda callback, state, user_role=None: handle_month_control_back_to_result(callback, state),
        states_group.month_control_result_display: lambda callback, state, user_role=None: handle_month_control_back_to_result(callback, state),

        # Обработчики для пробного ЕНТ (студенты)
        states_group.ent_select_group: lambda callback, state, user_role=None: show_ent_groups(callback, state),
        states_group.ent_select_student: lambda callback, state, user_role=None: show_ent_students(callback, state),
        states_group.ent_result: lambda callback, state, user_role=None: handle_ent_history_back_to_result(callback, state),
        states_group.ent_result_display: lambda callback, state, user_role=None: show_ent_statistics(callback, state)
    }

    # Добавляем обработчики для состояний микротем, если они есть
    if hasattr(states_group, 'month_entry_detailed_microtopics'):
        STATE_HANDLERS[states_group.month_entry_detailed_microtopics] = lambda callback, state, user_role=None: handle_month_entry_back_to_result(callback, state)
    if hasattr(states_group, 'month_entry_summary_microtopics'):
        STATE_HANDLERS[states_group.month_entry_summary_microtopics] = lambda callback, state, user_role=None: handle_month_entry_back_to_result(callback, state)

    # Добавляем обработчики для состояний микротем контрольного теста месяца, если они есть
    if hasattr(states_group, 'month_control_detailed_microtopics'):
        STATE_HANDLERS[states_group.month_control_detailed_microtopics] = lambda callback, state, user_role=None: handle_month_control_back_to_result(callback, state)
    if hasattr(states_group, 'month_control_summary_microtopics'):
        STATE_HANDLERS[states_group.month_control_summary_microtopics] = lambda callback, state, user_role=None: handle_month_control_back_to_result(callback, state)

    # Добавляем обработчики для истории пробного ЕНТ, если состояния существуют
    if hasattr(states_group, 'ent_history'):
        STATE_HANDLERS.update({
            # Обработчики для истории пробного ЕНТ
            states_group.ent_history: lambda callback, state, user_role=None: handle_ent_history_back_to_result(callback, state),
            states_group.ent_history_detail: lambda callback, state, user_role=None: handle_ent_history_back_to_list(callback, state),
            states_group.ent_history_analytics_subjects: lambda callback, state, user_role=None: handle_ent_history_back_to_detail(callback, state),
            states_group.ent_history_subject_analytics_menu: lambda callback, state, user_role=None: handle_ent_history_back_to_subjects(callback, state),
            states_group.ent_history_subject_analytics_detailed: lambda callback, state, user_role=None: handle_ent_history_back_to_menu(callback, state),
            states_group.ent_history_subject_analytics_summary: lambda callback, state, user_role=None: handle_ent_history_back_to_menu(callback, state),
        })

    # Добавляем обработчики для студенческих состояний, которые могут использоваться в контексте истории ЕНТ
    from student.handlers.trial_ent import TrialEntStates
    STATE_HANDLERS.update({
        TrialEntStates.subject_analytics_menu: lambda callback, state, user_role=None: handle_ent_history_back_to_subjects(callback, state),
        TrialEntStates.subject_analytics_detailed: lambda callback, state, user_role=None: handle_ent_history_back_to_menu(callback, state),
        TrialEntStates.subject_analytics_summary: lambda callback, state, user_role=None: handle_ent_history_back_to_menu(callback, state),
    })

    return STATE_TRANSITIONS, STATE_HANDLERS


async def back_from_ent_history_microtopics_to_menu(callback: CallbackQuery, state: FSMContext, role: str):
    """
    Универсальная функция возврата из изображений микротем истории ЕНТ к меню аналитики
    """
    try:
        user_data = await state.get_data()
        selected_subject_code = user_data.get("selected_subject_code")

        if not selected_subject_code:
            # Если нет сохраненного предмета, возвращаемся к списку предметов
            from common.tests_statistics.handlers import show_ent_history_analytics_subjects
            await show_ent_history_analytics_subjects(callback, state)
            return

        # Получаем название предмета
        from common.trial_ent_service import TrialEntService
        subject_name = TrialEntService.get_subject_name(selected_subject_code)

        from student.keyboards.trial_ent import get_trial_ent_analytics_menu_kb

        # Удаляем сообщение с изображением и отправляем новое текстовое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            logger.error(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer(
            f"📊 Выбери тип аналитики по предмету {subject_name}:",
            reply_markup=get_trial_ent_analytics_menu_kb(selected_subject_code)
        )

        # Сохраняем роль в данных состояния
        await state.update_data(user_role=role)

        # Устанавливаем состояние в зависимости от роли
        if role == "curator":
            from curator.states.states_tests import CuratorTestsStatisticsStates
            await state.set_state(CuratorTestsStatisticsStates.ent_history_subject_analytics_menu)
        elif role == "teacher":
            from teacher.states.states_tests import TeacherTestsStatisticsStates
            await state.set_state(TeacherTestsStatisticsStates.ent_history_subject_analytics_menu)
        elif role == "manager":
            from manager.states.states_tests import ManagerTestsStatisticsStates
            await state.set_state(ManagerTestsStatisticsStates.ent_history_subject_analytics_menu)

    except Exception as e:
        logger.error(f"Ошибка при возврате из микротем истории ЕНТ: {e}")
        from common.tests_statistics.keyboards import get_back_kb
        await callback.message.edit_text(
            "❌ Произошла ошибка при навигации",
            reply_markup=get_back_kb()
        )


# Функции-обработчики для навигации назад в истории ЕНТ
async def handle_ent_history_back_to_result(callback: CallbackQuery, state: FSMContext):
    """Возврат из истории тестов к результату студента"""
    try:
        data = await state.get_data()
        group_id = data.get('selected_group_id')
        student_id = data.get('selected_student_id')

        logger.error(f"🔍 НАВИГАЦИЯ НАЗАД К РЕЗУЛЬТАТУ: group_id={group_id}, student_id={student_id}")
        logger.error(f"🔍 ВСЕ ДАННЫЕ СОСТОЯНИЯ: {data}")

        if group_id and student_id:
            # Вызываем функцию показа результата студента напрямую
            from common.tests_statistics.handlers import show_trial_ent_student_detail

            logger.error(f"🔍 ВЫЗЫВАЕМ show_trial_ent_student_detail НАПРЯМУЮ: group_id={group_id}, student_id={student_id}")

            await show_trial_ent_student_detail(callback, state, group_id, student_id)
        else:
            # Если данных нет, возвращаемся к выбору студентов
            logger.error(f"🔍 ДАННЫХ НЕТ, ВОЗВРАЩАЕМСЯ К ВЫБОРУ СТУДЕНТОВ")
            from common.tests_statistics.handlers import show_ent_students
            await show_ent_students(callback, state)
    except Exception as e:
        logger.error(f"❌ КРИТИЧЕСКАЯ ОШИБКА ПРИ ВОЗВРАТЕ К РЕЗУЛЬТАТУ: {e}")
        logger.error(f"❌ ТИП ОШИБКИ: {type(e).__name__}")
        logger.error(f"❌ ТРЕЙСБЕК:", exc_info=True)
        from common.tests_statistics.keyboards import get_back_kb
        await callback.message.edit_text(f"❌ Ошибка: {str(e)}", reply_markup=get_back_kb())


async def handle_ent_history_back_to_list(callback: CallbackQuery, state: FSMContext):
    """Возврат из детального просмотра теста к списку тестов"""
    try:
        data = await state.get_data()
        group_id = data.get('selected_group_id')
        student_id = data.get('selected_student_id')

        logger.error(f"🔍 НАВИГАЦИЯ НАЗАД К СПИСКУ: group_id={group_id}, student_id={student_id}")
        logger.error(f"🔍 ВСЕ ДАННЫЕ СОСТОЯНИЯ: {data}")
        logger.error(f"🔍 ТЕКУЩЕЕ СОСТОЯНИЕ: {await state.get_state()}")

        if group_id and student_id:
            # Создаем правильный callback для функции show_ent_student_history
            from common.tests_statistics.handlers import show_ent_student_history
            from aiogram.types import CallbackQuery as CBQ

            logger.error(f"🔍 СОЗДАЕМ CALLBACK ДЛЯ ИСТОРИИ: ent_history_{group_id}_{student_id}")

            # Создаем новый callback с правильными данными
            new_callback = CBQ(
                id=callback.id,
                from_user=callback.from_user,
                message=callback.message,
                data=f"ent_history_{group_id}_{student_id}",
                chat_instance=callback.chat_instance
            )

            await show_ent_student_history(new_callback, state)
        else:
            logger.error(f"🔍 ДАННЫХ НЕТ, ВОЗВРАЩАЕМСЯ К РЕЗУЛЬТАТУ")
            await handle_ent_history_back_to_result(callback, state)
    except Exception as e:
        logger.error(f"❌ КРИТИЧЕСКАЯ ОШИБКА ПРИ ВОЗВРАТЕ К СПИСКУ: {e}")
        logger.error(f"❌ ТИП ОШИБКИ: {type(e).__name__}")
        logger.error(f"❌ ТРЕЙСБЕК:", exc_info=True)
        from common.tests_statistics.keyboards import get_back_kb
        await callback.message.edit_text(f"❌ Ошибка: {str(e)}", reply_markup=get_back_kb())


async def handle_ent_history_back_to_detail(callback: CallbackQuery, state: FSMContext):
    """Возврат из выбора предметов к детальному просмотру теста"""
    try:
        data = await state.get_data()
        group_id = data.get('selected_group_id')
        student_id = data.get('selected_student_id')
        trial_ent_result_id = data.get('trial_ent_result_id')

        if group_id and student_id and trial_ent_result_id:
            # Создаем новый callback для показа детального просмотра
            from common.tests_statistics.handlers import show_ent_history_detail
            from aiogram.types import CallbackQuery as CBQ

            new_callback = CBQ(
                id=callback.id,
                from_user=callback.from_user,
                message=callback.message,
                data=f"ent_history_detail_{group_id}_{student_id}_{trial_ent_result_id}",
                chat_instance=callback.chat_instance
            )
            await show_ent_history_detail(new_callback, state)
        else:
            await handle_ent_history_back_to_list(callback, state)
    except Exception as e:
        logger.error(f"Ошибка при возврате к детальному просмотру: {e}")
        from common.tests_statistics.keyboards import get_back_kb
        await callback.message.edit_text("❌ Ошибка навигации", reply_markup=get_back_kb())


async def handle_ent_history_back_to_subjects(callback: CallbackQuery, state: FSMContext):
    """Возврат из меню аналитики к выбору предметов"""
    try:
        data = await state.get_data()
        logger.error(f"🔍 НАВИГАЦИЯ НАЗАД К ПРЕДМЕТАМ")
        logger.error(f"🔍 ВСЕ ДАННЫЕ СОСТОЯНИЯ: {data}")
        logger.error(f"🔍 ТЕКУЩЕЕ СОСТОЯНИЕ: {await state.get_state()}")

        from common.tests_statistics.handlers import show_ent_history_analytics_subjects
        await show_ent_history_analytics_subjects(callback, state)
    except Exception as e:
        logger.error(f"❌ КРИТИЧЕСКАЯ ОШИБКА ПРИ ВОЗВРАТЕ К ПРЕДМЕТАМ: {e}")
        logger.error(f"❌ ТИП ОШИБКИ: {type(e).__name__}")
        logger.error(f"❌ ТРЕЙСБЕК:", exc_info=True)
        from common.tests_statistics.keyboards import get_back_kb
        await callback.message.edit_text(f"❌ Ошибка: {str(e)}", reply_markup=get_back_kb())


async def handle_ent_history_back_to_menu(callback: CallbackQuery, state: FSMContext):
    """Возврат из аналитики микротем к меню выбора типа аналитики"""
    try:
        data = await state.get_data()
        selected_subject_code = data.get("selected_subject_code")

        if selected_subject_code:
            # Создаем новый callback для показа меню аналитики
            from common.tests_statistics.handlers import show_ent_history_subject_analytics_menu
            from aiogram.types import CallbackQuery as CBQ

            new_callback = CBQ(
                id=callback.id,
                from_user=callback.from_user,
                message=callback.message,
                data=f"analytics_{selected_subject_code}",
                chat_instance=callback.chat_instance
            )
            await show_ent_history_subject_analytics_menu(new_callback, state)
        else:
            await handle_ent_history_back_to_subjects(callback, state)
    except Exception as e:
        logger.error(f"Ошибка при возврате к меню: {e}")
        from common.tests_statistics.keyboards import get_back_kb
        # Удаляем сообщение с изображением и отправляем новое
        try:
            await callback.message.delete()
        except Exception as delete_error:
            logger.error(f"Не удалось удалить сообщение: {delete_error}")

        await callback.message.answer("❌ Ошибка навигации", reply_markup=get_back_kb())

def register_test_statistics_handlers(router, states_group, role):
    """
    Регистрирует обработчики для статистики тестов

    Args:
        router: Роутер для регистрации обработчиков
        states_group: Группа состояний (CuratorTestsStatisticsStates или TeacherTestsStatisticsStates)
        role: Роль пользователя ('curator', 'teacher' или 'manager')
    """
    logger = logging.getLogger(__name__)



    # Регистрируем обработчики микротем для входного теста месяца (если есть соответствующие состояния)
    if hasattr(states_group, 'month_entry_detailed_microtopics') and hasattr(states_group, 'month_entry_summary_microtopics'):


        from common.microtopics.register_handlers import register_curator_month_entry_test_microtopics_handlers
        from common.tests_statistics.keyboards import get_back_kb

        # Определяем префиксы callback_data в зависимости от роли
        detailed_callback_prefix = f"{role}_month_entry_page"
        summary_callback_prefix = f"{role}_month_entry_summary_page"

        register_curator_month_entry_test_microtopics_handlers(
            router=router,
            states_group=states_group,
            detailed_callback_prefix=detailed_callback_prefix,
            summary_callback_prefix=summary_callback_prefix,
            detailed_state=states_group.month_entry_detailed_microtopics,
            summary_state=states_group.month_entry_summary_microtopics,
            result_state=states_group.month_entry_result,
            back_keyboard_func=get_back_kb,
            premium_check=False  # Роли кроме студента имеют доступ к статистике
        )

    # Регистрируем обработчики микротем для контрольного теста месяца (если есть соответствующие состояния)
    if hasattr(states_group, 'month_control_detailed_microtopics') and hasattr(states_group, 'month_control_summary_microtopics'):


        from common.microtopics.register_handlers import register_month_control_test_microtopics_handlers

        # Определяем префиксы callback_data в зависимости от роли
        control_detailed_callback_prefix = f"{role}_month_control_page"
        control_summary_callback_prefix = f"{role}_month_control_summary_page"

        register_month_control_test_microtopics_handlers(
            router=router,
            states_group=states_group,
            detailed_callback_prefix=control_detailed_callback_prefix,
            summary_callback_prefix=control_summary_callback_prefix,
            detailed_state=states_group.month_control_detailed_microtopics,
            summary_state=states_group.month_control_summary_microtopics,
            result_state=states_group.month_control_result,
            back_keyboard_func=get_back_kb,
            premium_check=False  # Роли кроме студента имеют доступ к статистике
        )

    # Регистрируем обработчики микротем для входного теста курса (если есть соответствующие состояния)
    if hasattr(states_group, 'course_entry_detailed_microtopics') and hasattr(states_group, 'course_entry_summary_microtopics'):


        from common.microtopics.register_handlers import register_course_entry_test_microtopics_handlers

        # Определяем префиксы callback_data в зависимости от роли
        course_entry_detailed_callback_prefix = f"{role}_course_entry_page"
        course_entry_summary_callback_prefix = f"{role}_course_entry_summary_page"

        register_course_entry_test_microtopics_handlers(
            router=router,
            states_group=states_group,
            detailed_callback_prefix=course_entry_detailed_callback_prefix,
            summary_callback_prefix=course_entry_summary_callback_prefix,
            detailed_state=states_group.course_entry_detailed_microtopics,
            summary_state=states_group.course_entry_summary_microtopics,
            result_state=states_group.course_entry_result,
            back_keyboard_func=get_back_kb,
            premium_check=False  # Роли кроме студента имеют доступ к статистике
        )

    # Регистрируем обработчики пагинации для истории пробного ЕНТ (если есть соответствующие состояния)
    if hasattr(states_group, 'ent_history_subject_analytics_detailed') and hasattr(states_group, 'ent_history_subject_analytics_summary'):


        # Определяем префиксы callback_data в зависимости от роли
        detailed_callback_prefix = f"{role}_trial_ent_page"
        summary_callback_prefix = f"{role}_trial_ent_summary_page"

        # Обработчики пагинации для детальной аналитики истории ЕНТ
        @router.callback_query(states_group.ent_history_subject_analytics_detailed, F.data.startswith(f"{detailed_callback_prefix}_"))
        async def handle_ent_history_detailed_pagination(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: handle_ent_history_detailed_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.microtopics.handlers import handle_microtopics_pagination_universal
            await handle_microtopics_pagination_universal(
                callback=callback,
                state=state,
                callback_prefix=detailed_callback_prefix,
                display_mode="detailed",
                role=role
            )

        # Обработчики пагинации для сводной аналитики истории ЕНТ
        @router.callback_query(states_group.ent_history_subject_analytics_summary, F.data.startswith(f"{summary_callback_prefix}_"))
        async def handle_ent_history_summary_pagination(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: handle_ent_history_summary_pagination | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.microtopics.handlers import handle_microtopics_pagination_universal
            await handle_microtopics_pagination_universal(
                callback=callback,
                state=state,
                callback_prefix=summary_callback_prefix,
                display_mode="summary",
                role=role
            )

        # Регистрируем основные обработчики истории ЕНТ


        # Обработчик показа истории тестов студента
        @router.callback_query(states_group.ent_result, F.data.startswith("ent_history_"))
        async def show_ent_student_history_handler(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: show_ent_student_history_handler | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.tests_statistics.handlers import show_ent_student_history
            await show_ent_student_history(callback, state)

        # Обработчик детального просмотра теста из истории
        @router.callback_query(states_group.ent_history, F.data.startswith("ent_history_detail_"))
        async def show_ent_history_detail_handler(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: show_ent_history_detail_handler | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.tests_statistics.handlers import show_ent_history_detail
            await show_ent_history_detail(callback, state)

        # Обработчик показа предметов для аналитики
        @router.callback_query(states_group.ent_history_detail, F.data == "ent_history_analytics")
        async def show_ent_history_analytics_subjects_handler(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: show_ent_history_analytics_subjects_handler | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.tests_statistics.handlers import show_ent_history_analytics_subjects
            await show_ent_history_analytics_subjects(callback, state)

        # Обработчик выбора предмета для аналитики
        @router.callback_query(states_group.ent_history_analytics_subjects, F.data.startswith("analytics_"))
        async def show_ent_history_subject_analytics_menu_handler(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: show_ent_history_subject_analytics_menu_handler | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.tests_statistics.handlers import show_ent_history_subject_analytics_menu
            await show_ent_history_subject_analytics_menu(callback, state)

        # Обработчик детальной аналитики
        @router.callback_query(states_group.ent_history_subject_analytics_menu, F.data.startswith("trial_ent_detailed_"))
        async def show_ent_history_detailed_analytics_handler(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: show_ent_history_detailed_analytics_handler | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.tests_statistics.handlers import show_ent_history_detailed_analytics
            await show_ent_history_detailed_analytics(callback, state)

        # Обработчик сводной аналитики
        @router.callback_query(states_group.ent_history_subject_analytics_menu, F.data.startswith("trial_ent_summary_"))
        async def show_ent_history_summary_analytics_handler(callback: CallbackQuery, state: FSMContext):
            logger.info(f"ВЫЗОВ: show_ent_history_summary_analytics_handler | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            from common.tests_statistics.handlers import show_ent_history_summary_analytics
            await show_ent_history_summary_analytics(callback, state)

        # Обработчики возврата из изображений микротем истории ЕНТ
        @router.callback_query(states_group.ent_history_subject_analytics_detailed, F.data == "back_from_microtopics_image")
        async def back_from_ent_history_detailed_microtopics_image(callback: CallbackQuery, state: FSMContext):
            """Возврат из детальной аналитики микротем истории ЕНТ"""
            logger.info(f"ВЫЗОВ: back_from_ent_history_detailed_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            await back_from_ent_history_microtopics_to_menu(callback, state, role)

        @router.callback_query(states_group.ent_history_subject_analytics_summary, F.data == "back_from_microtopics_image")
        async def back_from_ent_history_summary_microtopics_image(callback: CallbackQuery, state: FSMContext):
            """Возврат из сводной аналитики микротем истории ЕНТ"""
            logger.info(f"ВЫЗОВ: back_from_ent_history_summary_microtopics_image | РОЛЬ: {role} | КОЛБЭК: {callback.data} | СОСТОЯНИЕ: {await state.get_state()}")
            await back_from_ent_history_microtopics_to_menu(callback, state, role)





    # Обработчик для главного меню статистики тестов
    @router.callback_query(F.data == f"{role}_tests")
    async def show_role_tests_statistics(callback: CallbackQuery, state: FSMContext):
        """Показать меню статистики тестов"""
        logger.info(f"Вызвана функция show_tests_statistics для пользователя {callback.from_user.id}")
        await show_tests_statistics_menu(callback, state, role)
        await state.set_state(states_group.main)

    # Для менеджера не регистрируем стандартные обработчики тестов,
    # так как у него есть свои обработчики с выбором персонала
    if role == "manager":
        return

    # Регистрируем обработчики для различных типов тестов

    # Входной тест курса (работа с незарегистрированными пользователями)
    @router.callback_query(states_group.main, F.data == "stats_course_entry_test")
    async def role_show_course_entry_subjects_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_course_entry_groups для пользователя {callback.from_user.id}")
        await show_course_entry_groups(callback, state)
        await state.set_state(states_group.course_entry_select_subject)

    @router.callback_query(states_group.course_entry_select_subject, F.data.startswith("course_entry_subject_"))
    async def role_show_course_entry_users_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_course_entry_statistics для пользователя {callback.from_user.id}")
        await show_course_entry_statistics(callback, state)
        await state.set_state(states_group.course_entry_select_user)

    # Обработчик для перехода к статистике конкретного незарегистрированного пользователя
    @router.callback_query(states_group.course_entry_select_user, F.data.startswith("course_entry_user_"))
    async def role_show_course_entry_user_detail_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_course_entry_student_statistics для пользователя {callback.from_user.id}")
        from common.tests_statistics.handlers import show_course_entry_student_statistics
        await show_course_entry_student_statistics(callback, state)
        await state.set_state(states_group.course_entry_result)



    # Входной тест месяца (работа со студентами)
    @router.callback_query(states_group.main, F.data == "stats_month_entry_test")
    async def role_show_month_entry_groups_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_entry_groups для пользователя {callback.from_user.id}")
        await show_month_entry_groups(callback, state)
        await state.set_state(states_group.month_entry_select_group)

    @router.callback_query(states_group.month_entry_select_group, F.data.startswith("month_entry_group_"))
    async def role_show_month_entry_months_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_entry_months для пользователя {callback.from_user.id}")
        await show_month_entry_months(callback, state)
        await state.set_state(states_group.month_entry_select_month)

    @router.callback_query(states_group.month_entry_select_month, F.data.startswith("month_entry_month_"))
    async def role_show_month_entry_students_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_entry_statistics для пользователя {callback.from_user.id}")
        await show_month_entry_statistics(callback, state)
        await state.set_state(states_group.month_entry_select_student)

    # Обработчик для перехода к статистике конкретного студента
    @router.callback_query(states_group.month_entry_select_student, F.data.startswith("month_entry_student_"))
    async def role_show_month_entry_student_detail_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_entry_student_statistics для пользователя {callback.from_user.id}")
        from common.tests_statistics.handlers import show_month_entry_student_statistics
        await show_month_entry_student_statistics(callback, state)
        await state.set_state(states_group.month_entry_result)

    # Обработчики для детальной аналитики входного теста месяца
    @router.callback_query(states_group.month_entry_result, F.data.startswith("month_entry_detailed_"))
    async def role_show_month_entry_detailed_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_entry_detailed для пользователя {callback.from_user.id}")
        await show_month_entry_detailed_microtopics(callback, state)
        # НЕ устанавливаем состояние здесь - оно уже установлено в адаптере
        # if hasattr(states_group, 'month_entry_detailed_microtopics'):
        #     await state.set_state(states_group.month_entry_detailed_microtopics)
        # else:
        #     await state.set_state(states_group.month_entry_result_display)

    @router.callback_query(states_group.month_entry_result, F.data.startswith("month_entry_summary_"))
    async def role_show_month_entry_summary_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_entry_summary для пользователя {callback.from_user.id}")
        await show_month_entry_summary_microtopics(callback, state)
        # НЕ устанавливаем состояние здесь - оно уже установлено в адаптере
        # if hasattr(states_group, 'month_entry_summary_microtopics'):
        #     await state.set_state(states_group.month_entry_summary_microtopics)
        # else:
        #     await state.set_state(states_group.month_entry_result_display)



    # Контрольный тест месяца (работа со студентами)
    @router.callback_query(states_group.main, F.data == "stats_month_control_test")
    async def role_show_month_control_groups_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_control_groups для пользователя {callback.from_user.id}")
        await show_month_control_groups(callback, state)
        await state.set_state(states_group.month_control_select_group)

    @router.callback_query(states_group.month_control_select_group, F.data.startswith("month_control_group_"))
    async def role_show_month_control_months_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_control_months для пользователя {callback.from_user.id}")
        await show_month_control_months(callback, state)
        await state.set_state(states_group.month_control_select_month)

    @router.callback_query(states_group.month_control_select_month, F.data.startswith("month_control_month_"))
    async def role_show_month_control_students_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_control_statistics для пользователя {callback.from_user.id}")
        await show_month_control_statistics(callback, state)
        await state.set_state(states_group.month_control_select_student)

    # Обработчик для перехода к статистике конкретного студента
    @router.callback_query(states_group.month_control_select_student, F.data.startswith("month_control_student_"))
    async def role_show_month_control_student_detail_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_control_student_statistics для пользователя {callback.from_user.id}")

        # Сначала удаляем предыдущее сообщение со списком студентов
        try:
            await callback.message.delete()
        except Exception as e:
            logger.info(f"Не удалось удалить предыдущее сообщение: {e}")

        from common.tests_statistics.handlers import show_month_control_student_statistics_universal
        await show_month_control_student_statistics_universal(callback, state)
        await state.set_state(states_group.month_control_result)

    # Обработчики для детальной аналитики контрольного теста месяца
    @router.callback_query(states_group.month_control_result, F.data.startswith("month_control_detailed_"))
    async def role_show_month_control_detailed_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_control_detailed для пользователя {callback.from_user.id}")
        await show_month_control_detailed_microtopics(callback, state)
        # НЕ устанавливаем состояние здесь - оно уже установлено в адаптере
        # if hasattr(states_group, 'month_control_detailed_microtopics'):
        #     await state.set_state(states_group.month_control_detailed_microtopics)
        # else:
        #     await state.set_state(states_group.month_control_result_display)

    @router.callback_query(states_group.month_control_result, F.data.startswith("month_control_summary_"))
    async def role_show_month_control_summary_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_month_control_summary для пользователя {callback.from_user.id}")
        await show_month_control_summary_microtopics(callback, state)
        # НЕ устанавливаем состояние здесь - оно уже установлено в адаптере
        # if hasattr(states_group, 'month_control_summary_microtopics'):
        #     await state.set_state(states_group.month_control_summary_microtopics)
        # else:
        #     await state.set_state(states_group.month_control_result_display)





    # Пробное ЕНТ (работа со студентами)
    @router.callback_query(states_group.main, F.data == "stats_ent_test")
    async def role_show_ent_groups_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_ent_groups для пользователя {callback.from_user.id}")
        await show_ent_groups(callback, state)
        await state.set_state(states_group.ent_select_group)

    @router.callback_query(states_group.ent_select_group, F.data.startswith("ent_group_"))
    async def role_show_ent_students_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_ent_students для пользователя {callback.from_user.id}")
        await show_ent_students(callback, state)
        await state.set_state(states_group.ent_select_student)

    @router.callback_query(states_group.ent_select_student, F.data.startswith("ent_student_"))
    async def role_show_ent_student_detail_handler(callback: CallbackQuery, state: FSMContext):
        logger.info(f"Вызвана функция show_ent_statistics для пользователя {callback.from_user.id}")
        await show_ent_statistics(callback, state)
        await state.set_state(states_group.ent_result)